"""
Simple test cho Slide Generation API
"""

import requests
import json

def test_template_info():
    """Test template info với một template ID mẫu"""
    
    # Sử dụng một template ID mẫu từ Google Slides
    # <PERSON><PERSON><PERSON> là template mẫu, bạ<PERSON> có thể thay thế bằng template thực tế
    template_id = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
    
    print(f"Testing template info for: {template_id}")
    
    url = f"http://localhost:8000/api/v1/slides/template-info/{template_id}"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(json.dumps(result, indent=2))
        else:
            print("❌ Error:")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_template_info()
