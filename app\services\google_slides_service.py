"""
Google Slides Service
Xử lý tương tác với Google Slides API để tạo slide tự động từ template
"""

import logging
import threading
import os
from typing import Dict, Any, List, Optional
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.core.config import settings

logger = logging.getLogger(__name__)


class GoogleSlidesService:
    """
    Service để quản lý Google Slides API
    Singleton pattern với Lazy Initialization
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Singleton pattern implementation với thread-safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(GoogleSlidesService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Lazy initialization - chỉ khởi tạo một lần"""
        if self._initialized:
            return

        self.slides_service = None
        self.drive_service = None
        self.credentials = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        """Ensure Google Slides service is initialized"""
        if not self._service_initialized:
            logger.info("🔄 GoogleSlidesService: First-time initialization triggered")
            self._initialize_service()
            self._service_initialized = True
            logger.info("✅ GoogleSlidesService: Initialization completed")

    def _initialize_service(self):
        """Khởi tạo Google Slides service"""
        try:
            # Kiểm tra xem có service account credentials không
            service_account_path = getattr(settings, 'GOOGLE_DRIVE_CREDENTIALS_PATH', None)

            if service_account_path and os.path.exists(service_account_path):
                # Sử dụng Service Account credentials
                SCOPES = [
                    'https://www.googleapis.com/auth/presentations',
                    'https://www.googleapis.com/auth/drive.file'
                ]

                self.credentials = service_account.Credentials.from_service_account_file(
                    service_account_path, scopes=SCOPES
                )

                # Tạo services
                self.slides_service = build('slides', 'v1', credentials=self.credentials)
                self.drive_service = build('drive', 'v3', credentials=self.credentials)
                logger.info("Google Slides service initialized with Service Account")

            else:
                # Fallback: Thông báo cần cấu hình credentials
                logger.warning("""
Google Slides service requires proper authentication setup.
Please configure one of the following:
1. Set GOOGLE_DRIVE_CREDENTIALS_PATH to a service account JSON file
2. Implement OAuth 2.0 flow for user authentication

For now, service will be disabled.
                """)
                return

        except Exception as e:
            logger.error(f"Failed to initialize Google Slides service: {e}")
            self.slides_service = None
            self.drive_service = None

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        self._ensure_service_initialized()
        return self.slides_service is not None and self.drive_service is not None

    async def analyze_template_structure(self, template_id: str) -> Dict[str, Any]:
        """
        Phân tích cấu trúc template Google Slides
        
        Args:
            template_id: ID của Google Slides template
            
        Returns:
            Dict chứa thông tin layouts và placeholders
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            # Lấy thông tin presentation
            presentation = self.slides_service.presentations().get(
                presentationId=template_id
            ).execute()

            # Phân tích layouts
            layouts = []
            if 'layouts' in presentation:
                for layout in presentation['layouts']:
                    layout_info = {
                        "layoutId": layout.get('objectId'),
                        "name": layout.get('layoutProperties', {}).get('displayName', 'Unnamed Layout'),
                        "placeholders": []
                    }
                    
                    # Lấy placeholders từ layout
                    if 'pageElements' in layout:
                        for element in layout['pageElements']:
                            if 'placeholder' in element:
                                placeholder = element['placeholder']
                                placeholder_info = {
                                    "objectId": element.get('objectId'),
                                    "type": placeholder.get('type', 'NONE'),
                                    "index": placeholder.get('index', 0)
                                }
                                layout_info["placeholders"].append(placeholder_info)
                    
                    layouts.append(layout_info)

            return {
                "success": True,
                "template_id": template_id,
                "title": presentation.get('title', 'Untitled'),
                "layouts": layouts,
                "slide_count": len(presentation.get('slides', []))
            }

        except HttpError as e:
            logger.error(f"HTTP error analyzing template {template_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error analyzing template {template_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def copy_template(self, template_id: str, new_title: str) -> Dict[str, Any]:
        """
        Copy template thành file mới
        
        Args:
            template_id: ID của template gốc
            new_title: Tên file mới
            
        Returns:
            Dict chứa thông tin file đã copy
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            # Copy file sử dụng Drive API
            copied_file = self.drive_service.files().copy(
                fileId=template_id,
                body={'name': new_title}
            ).execute()

            # Tạo permission để share
            permission = {
                'type': 'anyone',
                'role': 'writer'  # Cho phép edit
            }
            self.drive_service.permissions().create(
                fileId=copied_file.get('id'),
                body=permission
            ).execute()

            return {
                "success": True,
                "file_id": copied_file.get('id'),
                "name": copied_file.get('name'),
                "web_view_link": f"https://docs.google.com/presentation/d/{copied_file.get('id')}/edit"
            }

        except HttpError as e:
            logger.error(f"HTTP error copying template {template_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error copying template {template_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def create_slides_from_content(
        self, 
        presentation_id: str, 
        slides_content: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Tạo slides từ nội dung đã được LLM sinh ra
        
        Args:
            presentation_id: ID của presentation đã copy
            slides_content: List các slide content từ LLM
            
        Returns:
            Dict kết quả tạo slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            requests = []
            
            # Xóa slides mặc định nếu có (trừ slide đầu tiên)
            presentation = self.slides_service.presentations().get(
                presentationId=presentation_id
            ).execute()
            
            existing_slides = presentation.get('slides', [])
            if len(existing_slides) > 1:
                for slide in existing_slides[1:]:  # Giữ lại slide đầu tiên
                    requests.append({
                        'deleteObject': {
                            'objectId': slide['objectId']
                        }
                    })

            # Tạo slides mới theo content
            for i, slide_content in enumerate(slides_content):
                if i == 0:
                    # Sử dụng slide đầu tiên có sẵn
                    slide_id = existing_slides[0]['objectId']
                else:
                    # Tạo slide mới
                    slide_id = f"slide_{i}"
                    requests.append({
                        'createSlide': {
                            'objectId': slide_id,
                            'slideLayoutReference': {
                                'layoutId': slide_content.get('layoutId')
                            }
                        }
                    })

                # Thêm nội dung vào slide
                for field_id, content in slide_content.get('fields', {}).items():
                    requests.append({
                        'insertText': {
                            'objectId': field_id,
                            'text': content
                        }
                    })

            # Thực hiện batch update
            if requests:
                self.slides_service.presentations().batchUpdate(
                    presentationId=presentation_id,
                    body={'requests': requests}
                ).execute()

            return {
                "success": True,
                "presentation_id": presentation_id,
                "slides_created": len(slides_content)
            }

        except HttpError as e:
            logger.error(f"HTTP error creating slides for {presentation_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error creating slides for {presentation_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }


# Hàm để lấy singleton instance
def get_google_slides_service() -> GoogleSlidesService:
    """
    Lấy singleton instance của GoogleSlidesService
    Thread-safe lazy initialization

    Returns:
        GoogleSlidesService: Singleton instance
    """
    return GoogleSlidesService()
