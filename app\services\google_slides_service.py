import logging
import threading
import os
from typing import Dict, Any, List, Optional
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.core.config import settings

logger = logging.getLogger(__name__)

class GoogleSlidesService:
    """
    Service để quản lý Google Slides API
    Singleton pattern với Lazy Initialization
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(GoogleSlidesService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self.slides_service = None
        self.drive_service = None
        self.credentials = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        if not self._service_initialized:
            logger.info("🔄 GoogleSlidesService: First-time initialization triggered")
            self._initialize_service()
            self._service_initialized = True
            logger.info("✅ GoogleSlidesService: Initialization completed")

    def _initialize_service(self):
        try:
            credentials_path = "google_client_Dat.json"
            if os.path.exists(credentials_path):
                SCOPES = [
                    'https://www.googleapis.com/auth/presentations',
                    'https://www.googleapis.com/auth/drive.file'
                ]
                self.credentials = service_account.Credentials.from_service_account_file(
                    credentials_path, scopes=SCOPES
                )
                self.slides_service = build('slides', 'v1', credentials=self.credentials)
                self.drive_service = build('drive', 'v3', credentials=self.credentials)
                logger.info("Google Slides service initialized with google_client_Dat.json")
            else:
                service_account_path = getattr(settings, 'GOOGLE_DRIVE_CREDENTIALS_PATH', None)
                if service_account_path and os.path.exists(service_account_path):
                    SCOPES = [
                        'https://www.googleapis.com/auth/presentations',
                        'https://www.googleapis.com/auth/drive.file'
                    ]
                    self.credentials = service_account.Credentials.from_service_account_file(
                        service_account_path, scopes=SCOPES
                    )
                    self.slides_service = build('slides', 'v1', credentials=self.credentials)
                    self.drive_service = build('drive', 'v3', credentials=self.credentials)
                    logger.info("Google Slides service initialized with GOOGLE_DRIVE_CREDENTIALS_PATH")
                else:
                    logger.warning("""
Google Slides service requires Service Account credentials.
Please ensure google_client_Dat.json exists in the project root
or set GOOGLE_DRIVE_CREDENTIALS_PATH to a service account JSON file.
Service will be disabled.
                    """)
                    return
        except Exception as e:
            logger.error(f"Failed to initialize Google Slides service: {e}")
            self.slides_service = None
            self.drive_service = None

    def is_available(self) -> bool:
        self._ensure_service_initialized()
        return self.slides_service is not None and self.drive_service is not None

    async def analyze_template_structure(self, template_id: str) -> Dict[str, Any]:
        """
        Phân tích cấu trúc template Google Slides (chuẩn lấy placeholder theo layout thực tế sử dụng)
        Args:
            template_id: ID của Google Slides template
        Returns:
            Dict chứa thông tin layouts và placeholders
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            # Lấy toàn bộ thông tin presentation
            presentation = self.slides_service.presentations().get(
                presentationId=template_id
            ).execute()

            # Map layoutId -> layoutName
            layout_name_map = {}
            if 'layouts' in presentation:
                for layout in presentation['layouts']:
                    layout_id = layout.get('objectId')
                    layout_name = layout.get('layoutProperties', {}).get('displayName', 'Unnamed Layout')
                    layout_name_map[layout_id] = layout_name

            # Gom placeholder từ từng slide theo layoutId
            layouts_info = {}

            for slide in presentation.get('slides', []):
                layout_ref = slide.get('slideLayoutReference', {})
                layout_id = layout_ref.get('layoutId')
                if not layout_id:
                    continue

                if layout_id not in layouts_info:
                    layouts_info[layout_id] = {
                        "layoutId": layout_id,
                        "name": layout_name_map.get(layout_id, 'Unnamed Layout'),
                        "placeholders": []
                    }
                    existing = set()
                else:
                    existing = { (p['type'], p['index']) for p in layouts_info[layout_id]["placeholders"] }

                for element in slide.get('pageElements', []):
                    placeholder = element.get('placeholder')
                    if placeholder:
                        key = (placeholder.get('type', 'NONE'), placeholder.get('index', 0))
                        if key not in existing:
                            placeholder_info = {
                                "objectId": element.get('objectId'),
                                "type": placeholder.get('type', 'NONE'),
                                "index": placeholder.get('index', 0)
                            }
                            layouts_info[layout_id]["placeholders"].append(placeholder_info)
                            existing.add(key)

            # Đưa ra list cho đúng format
            layouts = list(layouts_info.values())

            return {
                "success": True,
                "template_id": template_id,
                "title": presentation.get('title', 'Untitled'),
                "layouts": layouts,
                "slide_count": len(presentation.get('slides', []))
            }

        except HttpError as e:
            logger.error(f"HTTP error analyzing template {template_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error analyzing template {template_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def copy_template(self, template_id: str, new_title: str) -> Dict[str, Any]:
        """
        Copy template thành file mới

        Args:
            template_id: ID của template gốc
            new_title: Tên file mới

        Returns:
            Dict chứa thông tin file đã copy
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            # Copy file sử dụng Drive API
            copied_file = self.drive_service.files().copy(
                fileId=template_id,
                body={'name': new_title}
            ).execute()

            # Tạo permission để share
            permission = {
                'type': 'anyone',
                'role': 'writer'  # Cho phép edit
            }
            self.drive_service.permissions().create(
                fileId=copied_file.get('id'),
                body=permission
            ).execute()

            return {
                "success": True,
                "file_id": copied_file.get('id'),
                "name": copied_file.get('name'),
                "web_view_link": f"https://docs.google.com/presentation/d/{copied_file.get('id')}/edit"
            }

        except HttpError as e:
            logger.error(f"HTTP error copying template {template_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error copying template {template_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def create_slides_from_content(
        self,
        presentation_id: str,
        slides_content: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Tạo slides từ nội dung đã được LLM sinh ra

        Args:
            presentation_id: ID của presentation đã copy
            slides_content: List các slide content từ LLM

        Returns:
            Dict kết quả tạo slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            requests = []

            # Xóa slides mặc định nếu có (trừ slide đầu tiên)
            presentation = self.slides_service.presentations().get(
                presentationId=presentation_id
            ).execute()

            existing_slides = presentation.get('slides', [])
            if len(existing_slides) > 1:
                for slide in existing_slides[1:]:  # Giữ lại slide đầu tiên
                    requests.append({
                        'deleteObject': {
                            'objectId': slide['objectId']
                        }
                    })

            # Tạo slides mới theo content
            for i, slide_content in enumerate(slides_content):
                if i == 0:
                    # Sử dụng slide đầu tiên có sẵn
                    slide_id = existing_slides[0]['objectId']
                else:
                    # Tạo slide mới
                    slide_id = f"slide_{i}"
                    requests.append({
                        'createSlide': {
                            'objectId': slide_id,
                            'slideLayoutReference': {
                                'layoutId': slide_content.get('layoutId')
                            }
                        }
                    })

                # Thêm nội dung vào slide
                for field_id, content in slide_content.get('fields', {}).items():
                    requests.append({
                        'insertText': {
                            'objectId': field_id,
                            'text': content
                        }
                    })

            # Thực hiện batch update
            if requests:
                self.slides_service.presentations().batchUpdate(
                    presentationId=presentation_id,
                    body={'requests': requests}
                ).execute()

            return {
                "success": True,
                "presentation_id": presentation_id,
                "slides_created": len(slides_content)
            }

        except HttpError as e:
            logger.error(f"HTTP error creating slides for {presentation_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error creating slides for {presentation_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }


# Hàm để lấy singleton instance
def get_google_slides_service() -> GoogleSlidesService:
    """
    Lấy singleton instance của GoogleSlidesService
    Thread-safe lazy initialization

    Returns:
        GoogleSlidesService: Singleton instance
    """
    return GoogleSlidesService()
