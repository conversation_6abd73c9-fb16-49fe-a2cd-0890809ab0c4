import logging
import threading
import os
from typing import Dict, Any, List, Optional
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.core.config import settings

logger = logging.getLogger(__name__)


def extract_text_from_shape(shape: Dict[str, Any]) -> str:
    if 'text' in shape and 'textElements' in shape['text']:
        parts = []
        for te in shape['text']['textElements']:
            text_run = te.get('textRun')
            if text_run:
                parts.append(text_run.get('content', '').strip())
        return ' '.join(parts).strip()
    return ''


class GoogleSlidesService:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(GoogleSlidesService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        self.slides_service = None
        self.drive_service = None
        self.credentials = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        if not self._service_initialized:
            logger.info("🔄 GoogleSlidesService: First-time initialization triggered")
            self._initialize_service()
            self._service_initialized = True
            logger.info("✅ GoogleSlidesService: Initialization completed")

    def _initialize_service(self):
        try:
            credentials_path = "google_client_Dat.json"
            if os.path.exists(credentials_path):
                SCOPES = [
                    'https://www.googleapis.com/auth/presentations',
                    'https://www.googleapis.com/auth/drive.file'
                ]
                self.credentials = service_account.Credentials.from_service_account_file(
                    credentials_path, scopes=SCOPES
                )
                self.slides_service = build('slides', 'v1', credentials=self.credentials)
                self.drive_service = build('drive', 'v3', credentials=self.credentials)
                logger.info("Google Slides service initialized with google_client_Dat.json")
            else:
                service_account_path = getattr(settings, 'GOOGLE_DRIVE_CREDENTIALS_PATH', None)
                if service_account_path and os.path.exists(service_account_path):
                    SCOPES = [
                        'https://www.googleapis.com/auth/presentations',
                        'https://www.googleapis.com/auth/drive.file'
                    ]
                    self.credentials = service_account.Credentials.from_service_account_file(
                        service_account_path, scopes=SCOPES
                    )
                    self.slides_service = build('slides', 'v1', credentials=self.credentials)
                    self.drive_service = build('drive', 'v3', credentials=self.credentials)
                    logger.info("Google Slides service initialized with GOOGLE_DRIVE_CREDENTIALS_PATH")
                else:
                    logger.warning("""
Google Slides service requires Service Account credentials.
Please ensure google_client_Dat.json exists in the project root
or set GOOGLE_DRIVE_CREDENTIALS_PATH to a service account JSON file.
Service will be disabled.
                    """)
                    return
        except Exception as e:
            logger.error(f"Failed to initialize Google Slides service: {e}")
            self.slides_service = None
            self.drive_service = None

    def is_available(self) -> bool:
        self._ensure_service_initialized()
        return self.slides_service is not None and self.drive_service is not None

    async def copy_and_analyze_template(self, template_id: str, new_title: str) -> Dict[str, Any]:
        """
        Copy template và phân tích cấu trúc của bản sao (theo yêu cầu mới)

        Args:
            template_id: ID của Google Slides template gốc
            new_title: Tên cho file mới

        Returns:
            Dict chứa thông tin file đã copy và cấu trúc slides/elements
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            # Bước 1: Copy template thành file mới ngay từ đầu
            logger.info(f"Copying template {template_id} to new file: {new_title}")
            copy_result = await self.copy_template(template_id, new_title)
            if not copy_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to copy template: {copy_result['error']}"
                }

            copied_presentation_id = copy_result["file_id"]
            logger.info(f"Template copied successfully. New presentation ID: {copied_presentation_id}")

            # Bước 2: Phân tích cấu trúc của bản sao (không phải template gốc)
            presentation = self.slides_service.presentations().get(
                presentationId=copied_presentation_id
            ).execute()

            slides_info = []
            for slide in presentation.get('slides', []):
                slide_info = {
                    "slideId": slide.get("objectId"),
                    "elements": []
                }

                for element in slide.get('pageElements', []):
                    if 'shape' in element:
                        text = extract_text_from_shape(element['shape'])
                        if text:
                            slide_info['elements'].append({
                                "objectId": element.get('objectId'),
                                "text": text
                            })

                slides_info.append(slide_info)

            return {
                "success": True,
                "original_template_id": template_id,
                "copied_presentation_id": copied_presentation_id,
                "presentation_title": presentation.get('title', 'Untitled'),
                "web_view_link": copy_result["web_view_link"],
                "slide_count": len(presentation.get('slides', [])),
                "slides": slides_info
            }

        except HttpError as e:
            logger.error(f"HTTP error in copy_and_analyze_template: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error in copy_and_analyze_template: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def analyze_template_structure(self, template_id: str) -> Dict[str, Any]:
        """
        Phân tích cấu trúc template Google Slides (lấy text trên các slide)
        DEPRECATED: Sử dụng copy_and_analyze_template thay thế

        Args:
            template_id: ID của Google Slides template

        Returns:
            Dict chứa thông tin slides, objectId và text đang hiển thị
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            presentation = self.slides_service.presentations().get(
                presentationId=template_id
            ).execute()

            slides_info = []
            for slide in presentation.get('slides', []):
                slide_info = {
                    "slideId": slide.get("objectId"),
                    "elements": []
                }

                for element in slide.get('pageElements', []):
                    if 'shape' in element:
                        text = extract_text_from_shape(element['shape'])
                        if text:
                            slide_info['elements'].append({
                                "objectId": element.get('objectId'),
                                "text": text
                            })

                slides_info.append(slide_info)

            return {
                "success": True,
                "template_id": template_id,
                "title": presentation.get('title', 'Untitled'),
                "slide_count": len(presentation.get('slides', [])),
                "slides": slides_info
            }

        except HttpError as e:
            logger.error(f"HTTP error analyzing template {template_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error analyzing template {template_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def copy_template(self, template_id: str, new_title: str) -> Dict[str, Any]:
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            copied_file = self.drive_service.files().copy(
                fileId=template_id,
                body={'name': new_title}
            ).execute()

            permission = {
                'type': 'anyone',
                'role': 'writer'
            }
            self.drive_service.permissions().create(
                fileId=copied_file.get('id'),
                body=permission
            ).execute()

            return {
                "success": True,
                "file_id": copied_file.get('id'),
                "name": copied_file.get('name'),
                "web_view_link": f"https://docs.google.com/presentation/d/{copied_file.get('id')}/edit"
            }

        except HttpError as e:
            logger.error(f"HTTP error copying template {template_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error copying template {template_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def update_copied_presentation_content(
        self,
        presentation_id: str,
        slides_content: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Cập nhật nội dung vào presentation đã copy (theo quy trình mới)

        Args:
            presentation_id: ID của presentation đã copy
            slides_content: List nội dung slides từ LLM

        Returns:
            Dict kết quả cập nhật nội dung
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            requests = []

            presentation = self.slides_service.presentations().get(
                presentationId=presentation_id
            ).execute()

            existing_slides = presentation.get('slides', [])
            if len(existing_slides) > 1:
                for slide in existing_slides[1:]:
                    requests.append({
                        'deleteObject': {
                            'objectId': slide['objectId']
                        }
                    })

            for i, slide_content in enumerate(slides_content):
                if i == 0:
                    slide_id = existing_slides[0]['objectId']
                else:
                    slide_id = f"slide_{i}"
                    requests.append({
                        'createSlide': {
                            'objectId': slide_id,
                            'slideLayoutReference': {
                                'layoutId': slide_content.get('layoutId')
                            }
                        }
                    })

                for field_id, content in slide_content.get('fields', {}).items():
                    requests.append({
                        'insertText': {
                            'objectId': field_id,
                            'text': content
                        }
                    })

            if requests:
                self.slides_service.presentations().batchUpdate(
                    presentationId=presentation_id,
                    body={'requests': requests}
                ).execute()

            return {
                "success": True,
                "presentation_id": presentation_id,
                "slides_created": len(slides_content)
            }

        except HttpError as e:
            logger.error(f"HTTP error creating slides for {presentation_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error creating slides for {presentation_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }


def get_google_slides_service() -> GoogleSlidesService:
    return GoogleSlidesService()
