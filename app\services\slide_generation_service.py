"""
Slide Generation Service
Xử lý logic sinh nội dung slide từ lesson content và template structure sử dụng LLM
"""

import logging
import threading
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import TextbookRetrievalService
from app.services.google_slides_service import get_google_slides_service

logger = logging.getLogger(__name__)


class SlideGenerationService:
    """
    Service để sinh nội dung slide từ lesson content và template
    Singleton pattern với Lazy Initialization
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Singleton pattern implementation với thread-safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SlideGenerationService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Lazy initialization - chỉ khởi tạo một lần"""
        if self._initialized:
            return

        self.llm_service = None
        self.textbook_service = None
        self.slides_service = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        """Ensure services are initialized"""
        if not self._service_initialized:
            logger.info("🔄 SlideGenerationService: First-time initialization triggered")
            self.llm_service = get_llm_service()
            self.textbook_service = TextbookRetrievalService()
            self.slides_service = get_google_slides_service()
            self._service_initialized = True
            logger.info("✅ SlideGenerationService: Initialization completed")

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        self._ensure_service_initialized()
        return (self.llm_service and self.llm_service.is_available() and 
                self.slides_service and self.slides_service.is_available())

    async def generate_slides_from_lesson(
        self, 
        lesson_id: str, 
        template_id: str,
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides từ lesson_id và template_id
        
        Args:
            lesson_id: ID của bài học
            template_id: ID của Google Slides template
            config_prompt: Prompt cấu hình tùy chỉnh (optional)
            
        Returns:
            Dict chứa kết quả tạo slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Slide generation service not available"
            }

        try:
            logger.info(f"Starting slide generation for lesson {lesson_id} with template {template_id}")

            # Bước 1: Lấy nội dung bài học
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)
            if not lesson_result:
                return {
                    "success": False,
                    "error": f"Could not retrieve lesson content for {lesson_id}"
                }

            lesson_content = lesson_result.get("lesson_content", "")
            if not lesson_content:
                return {
                    "success": False,
                    "error": f"Empty lesson content for {lesson_id}"
                }

            # Bước 2: Phân tích cấu trúc template
            template_result = await self.slides_service.analyze_template_structure(template_id)
            if not template_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not analyze template: {template_result['error']}"
                }

            # Bước 3: Sinh nội dung slides bằng LLM
            slides_content = await self._generate_slides_content(
                lesson_content, 
                template_result, 
                config_prompt
            )
            if not slides_content["success"]:
                return slides_content

            # Bước 4: Copy template thành file mới
            new_title = f"Bài học {lesson_id} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            copy_result = await self.slides_service.copy_template(template_id, new_title)
            if not copy_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not copy template: {copy_result['error']}"
                }

            # Bước 5: Tạo slides với nội dung đã sinh
            create_result = await self.slides_service.create_slides_from_content(
                copy_result["file_id"],
                slides_content["slides"]
            )
            if not create_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not create slides: {create_result['error']}"
                }

            return {
                "success": True,
                "lesson_id": lesson_id,
                "template_id": template_id,
                "presentation_id": copy_result["file_id"],
                "presentation_title": new_title,
                "web_view_link": copy_result["web_view_link"],
                "slides_created": create_result["slides_created"],
                "template_info": {
                    "title": template_result["title"],
                    "layouts_count": len(template_result["layouts"])
                }
            }

        except Exception as e:
            logger.error(f"Error generating slides: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _generate_slides_content(
        self, 
        lesson_content: str, 
        template_structure: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Sinh nội dung slides bằng LLM
        
        Args:
            lesson_content: Nội dung bài học
            template_structure: Cấu trúc template từ Google Slides API
            config_prompt: Prompt cấu hình tùy chỉnh
            
        Returns:
            Dict chứa nội dung slides đã sinh
        """
        try:
            # Tạo prompt cho LLM
            prompt = self._create_slide_generation_prompt(
                lesson_content, 
                template_structure, 
                config_prompt
            )

            # Gọi LLM để sinh nội dung
            llm_result = await self.llm_service._generate_content(prompt)
            if not llm_result["success"]:
                return {
                    "success": False,
                    "error": f"LLM generation failed: {llm_result['error']}"
                }

            # Parse kết quả từ LLM
            try:
                slides_data = json.loads(llm_result["text"])
                if not isinstance(slides_data, list):
                    raise ValueError("LLM output must be a list of slides")

                return {
                    "success": True,
                    "slides": slides_data
                }

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM output as JSON: {e}")
                return {
                    "success": False,
                    "error": f"Invalid JSON from LLM: {e}"
                }

        except Exception as e:
            logger.error(f"Error generating slides content: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_slide_generation_prompt(
        self, 
        lesson_content: str, 
        template_structure: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> str:
        """
        Tạo prompt cho LLM để sinh nội dung slides
        
        Args:
            lesson_content: Nội dung bài học
            template_structure: Cấu trúc template
            config_prompt: Prompt cấu hình tùy chỉnh
            
        Returns:
            String prompt cho LLM
        """
        # Prompt cấu hình mặc định
        default_config = """
Hãy phân tích nội dung bài học và cấu trúc template slide. Tạo outline và nội dung cho từng slide phù hợp với các layout, mapping vào đúng các placeholder. 

Yêu cầu:
1. Có thể thêm hoặc bớt số slide để phù hợp độ dài bài học
2. Ảnh chỉ là mô tả bằng text (không cần URL thực tế)
3. Nội dung phải phù hợp với từng loại placeholder (TITLE, BODY, etc.)
4. Sinh thêm slide hoặc bỏ bớt slide không cần thiết để phù hợp với nội dung
5. Đảm bảo nội dung có tính giáo dục và dễ hiểu
"""

        # Sử dụng config_prompt nếu có, nếu không dùng mặc định
        final_config = config_prompt if config_prompt else default_config

        # Tạo thông tin về layouts
        layouts_info = "Các layout có sẵn trong template:\n"
        for layout in template_structure.get("layouts", []):
            layouts_info += f"- Layout ID: {layout['layoutId']}\n"
            layouts_info += f"  Tên: {layout['name']}\n"
            layouts_info += f"  Placeholders:\n"
            for placeholder in layout.get("placeholders", []):
                layouts_info += f"    * {placeholder['objectId']} (type: {placeholder['type']})\n"
            layouts_info += "\n"

        prompt = f"""
{final_config}

THÔNG TIN TEMPLATE:
Tiêu đề template: {template_structure.get('title', 'Không có tiêu đề')}
Số slide hiện tại: {template_structure.get('slide_count', 0)}

{layouts_info}

NỘI DUNG BÀI HỌC:
{lesson_content}

YÊU CẦU OUTPUT:
Trả về JSON array với format sau:
[
  {{
    "layoutId": "layout_id_from_template",
    "fields": {{
      "placeholder_object_id": "nội dung text cho placeholder này",
      "another_placeholder_id": "nội dung khác"
    }}
  }},
  ...
]

Lưu ý:
- Chỉ sử dụng các layoutId và placeholder objectId có trong template
- Nội dung phải phù hợp với loại placeholder (TITLE ngắn gọn, BODY chi tiết hơn)
- Có thể tạo nhiều slide cùng layout nếu cần
- Đảm bảo JSON format đúng và valid
"""

        return prompt


# Hàm để lấy singleton instance
def get_slide_generation_service() -> SlideGenerationService:
    """
    Lấy singleton instance của SlideGenerationService
    Thread-safe lazy initialization

    Returns:
        SlideGenerationService: Singleton instance
    """
    return SlideGenerationService()
