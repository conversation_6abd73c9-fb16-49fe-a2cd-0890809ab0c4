"""
Slide Generation Service
Xử lý logic sinh nội dung slide từ lesson content và template structure sử dụng LLM
"""

import logging
import threading
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.services.llm_service import get_llm_service
from app.services.textbook_retrieval_service import TextbookRetrievalService
from app.services.google_slides_service import get_google_slides_service

logger = logging.getLogger(__name__)


class SlideGenerationService:
    """
    Service để sinh nội dung slide từ lesson content và template
    Singleton pattern với Lazy Initialization
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Singleton pattern implementation với thread-safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SlideGenerationService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Lazy initialization - chỉ khởi tạo một lần"""
        if self._initialized:
            return

        self.llm_service = None
        self.textbook_service = None
        self.slides_service = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        """Ensure services are initialized"""
        if not self._service_initialized:
            logger.info("🔄 SlideGenerationService: First-time initialization triggered")
            self.llm_service = get_llm_service()
            self.textbook_service = TextbookRetrievalService()
            self.slides_service = get_google_slides_service()
            self._service_initialized = True
            logger.info("✅ SlideGenerationService: Initialization completed")

    def is_available(self) -> bool:
        """Kiểm tra service có sẵn sàng không"""
        self._ensure_service_initialized()
        return (self.llm_service and self.llm_service.is_available() and 
                self.slides_service and self.slides_service.is_available())

    async def generate_slides_from_lesson(
        self,
        lesson_id: str,
        template_id: str,
        config_prompt: Optional[str] = None,
        presentation_title: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tạo slides từ lesson_id và template_id (QUY TRÌNH MỚI)

        Args:
            lesson_id: ID của bài học
            template_id: ID của Google Slides template
            config_prompt: Prompt cấu hình tùy chỉnh (optional)
            presentation_title: Tiêu đề presentation tùy chỉnh (optional)

        Returns:
            Dict chứa kết quả tạo slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Slide generation service not available"
            }

        try:
            logger.info(f"Starting NEW slide generation process for lesson {lesson_id} with template {template_id}")

            # Bước 1: Lấy nội dung bài học
            lesson_result = await self.textbook_service.get_lesson_content(lesson_id)
            if not lesson_result:
                return {
                    "success": False,
                    "error": f"Could not retrieve lesson content for {lesson_id}"
                }

            lesson_content = lesson_result.get("lesson_content", "")
            if not lesson_content:
                return {
                    "success": False,
                    "error": f"Empty lesson content for {lesson_id}"
                }

            # Bước 2: Copy template và phân tích cấu trúc của bản sao (QUY TRÌNH MỚI)
            new_title = presentation_title or f"Bài học {lesson_id} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            copy_and_analyze_result = await self.slides_service.copy_and_analyze_template(template_id, new_title)
            if not copy_and_analyze_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not copy and analyze template: {copy_and_analyze_result['error']}"
                }

            # Bước 3: Sinh nội dung slides bằng LLM với cấu trúc của bản sao
            slides_content = await self._generate_slides_content(
                lesson_content,
                copy_and_analyze_result,
                config_prompt
            )
            if not slides_content["success"]:
                return slides_content

            # Bước 4: Cập nhật nội dung vào bản sao đã tạo
            update_result = await self.slides_service.update_copied_presentation_content(
                copy_and_analyze_result["copied_presentation_id"],
                slides_content["slides"]
            )
            if not update_result["success"]:
                return {
                    "success": False,
                    "error": f"Could not update presentation content: {update_result['error']}"
                }

            return {
                "success": True,
                "lesson_id": lesson_id,
                "original_template_id": template_id,
                "presentation_id": copy_and_analyze_result["copied_presentation_id"],
                "presentation_title": copy_and_analyze_result["presentation_title"],
                "web_view_link": copy_and_analyze_result["web_view_link"],
                "slides_created": update_result.get("slides_updated", len(slides_content["slides"])),
                "template_info": {
                    "title": copy_and_analyze_result["presentation_title"],
                    "slide_count": copy_and_analyze_result["slide_count"]
                }
            }

        except Exception as e:
            logger.error(f"Error generating slides: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _generate_slides_content(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Sinh nội dung slides bằng LLM (với cấu trúc từ bản sao)

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy và phân tích
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            Dict chứa nội dung slides đã sinh
        """
        try:
            # Tạo prompt cho LLM với thông tin từ bản sao
            prompt = self._create_slide_generation_prompt(
                lesson_content,
                copied_presentation_info,
                config_prompt
            )

            # Gọi LLM để sinh nội dung
            llm_result = await self.llm_service._generate_content(prompt)
            if not llm_result["success"]:
                return {
                    "success": False,
                    "error": f"LLM generation failed: {llm_result['error']}"
                }

            # Parse kết quả từ LLM
            try:
                slides_data = json.loads(llm_result["text"])
                if not isinstance(slides_data, list):
                    raise ValueError("LLM output must be a list of slides")

                return {
                    "success": True,
                    "slides": slides_data
                }

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM output as JSON: {e}")
                return {
                    "success": False,
                    "error": f"Invalid JSON from LLM: {e}"
                }

        except Exception as e:
            logger.error(f"Error generating slides content: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _create_slide_generation_prompt(
        self,
        lesson_content: str,
        copied_presentation_info: Dict[str, Any],
        config_prompt: Optional[str] = None
    ) -> str:
        """
        Tạo prompt cho LLM để sinh nội dung slides (với thông tin từ bản sao)

        Args:
            lesson_content: Nội dung bài học
            copied_presentation_info: Thông tin presentation đã copy
            config_prompt: Prompt cấu hình tùy chỉnh

        Returns:
            String prompt cho LLM
        """
        # Prompt cấu hình mặc định
        default_config = """
Hãy phân tích nội dung bài học và cấu trúc template slide. Tạo outline và nội dung cho từng slide phù hợp với các layout, mapping vào đúng các placeholder. 

Yêu cầu:
1. Có thể thêm hoặc bớt số slide để phù hợp độ dài bài học
2. Ảnh chỉ là mô tả bằng text (không cần URL thực tế)
3. Nội dung phải phù hợp với từng loại placeholder (TITLE, BODY, etc.)
4. Sinh thêm slide hoặc bỏ bớt slide không cần thiết để phù hợp với nội dung
5. Đảm bảo nội dung có tính giáo dục và dễ hiểu
"""

        # Sử dụng config_prompt nếu có, nếu không dùng mặc định
        final_config = config_prompt if config_prompt else default_config

        # Tạo thông tin về slides và elements từ bản sao
        slides_info = "Cấu trúc slides trong presentation đã copy:\n"
        for i, slide in enumerate(copied_presentation_info.get("slides", [])):
            slides_info += f"- Slide {i+1} (ID: {slide['slideId']}):\n"
            slides_info += f"  Elements có thể chỉnh sửa:\n"
            for element in slide.get("elements", []):
                slides_info += f"    * {element['objectId']}: \"{element['text'][:50]}...\"\n"
            slides_info += "\n"

        prompt = f"""
{final_config}

THÔNG TIN PRESENTATION ĐÃ COPY:
Tiêu đề: {copied_presentation_info.get('presentation_title', 'Không có tiêu đề')}
Số slide hiện tại: {copied_presentation_info.get('slide_count', 0)}
Presentation ID: {copied_presentation_info.get('copied_presentation_id')}

{slides_info}

NỘI DUNG BÀI HỌC:
{lesson_content}

YÊU CẦU OUTPUT:
Trả về JSON array với format sau:
[
  {{
    "slideId": "slide_id_from_copied_presentation",
    "updates": {{
      "element_object_id": "nội dung text mới cho element này",
      "another_element_id": "nội dung khác"
    }}
  }},
  ...
]

Lưu ý:
- Chỉ sử dụng các slideId và element objectId có trong presentation đã copy
- Thay thế nội dung hiện tại bằng nội dung phù hợp với bài học
- Giữ nguyên số lượng slide, chỉ cập nhật nội dung text
- Nội dung phải phù hợp với vị trí và kích thước của element
- Đảm bảo JSON format đúng và valid
"""

        return prompt


# Hàm để lấy singleton instance
def get_slide_generation_service() -> SlideGenerationService:
    """
    Lấy singleton instance của SlideGenerationService
    Thread-safe lazy initialization

    Returns:
        SlideGenerationService: Singleton instance
    """
    return SlideGenerationService()
