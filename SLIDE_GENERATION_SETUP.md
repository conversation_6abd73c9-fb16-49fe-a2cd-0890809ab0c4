# Hướng dẫn Setup API Tạo Slide Tự Động

## 1. <PERSON><PERSON><PERSON> hình Google Cloud Console

### Bước 1: Enable Google Slides API
1. <PERSON><PERSON><PERSON> cập [Google Cloud Console](https://console.cloud.google.com/)
2. Chọn project `planbook-465410`
3. Vào **APIs & Services** > **Library**
4. Tìm và enable các API sau:
   - **Google Slides API**
   - **Google Drive API** (nếu chưa có)

### Bước 2: Tạo Service Account (Khuyến nghị)
1. Vào **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **Service Account**
3. Điền thông tin service account
4. Tạo và download JSON key file
5. Đặt file vào thư mục project và cập nhật `GOOGLE_DRIVE_CREDENTIALS_PATH` trong `.env`

### Bước 3: <PERSON><PERSON><PERSON> hình OAuth 2.0 (<PERSON><PERSON><PERSON> cần user authentication)
1. Vào **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Chọn **Web application**
4. Thêm authorized redirect URIs
5. Download client configuration

## 2. Cấu hình Environment Variables

Thêm vào file `.env`:

```env
# Google Drive/Slides Configuration
GOOGLE_DRIVE_CREDENTIALS_PATH=path/to/service-account.json
GOOGLE_DRIVE_FOLDER_ID=your_folder_id
ENABLE_GOOGLE_DRIVE=true

# Celery Configuration (nếu chưa có)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

## 3. API Endpoints

### 3.1 Health Check
```
GET /api/v1/slides/health
```

### 3.2 Phân tích Template
```
GET /api/v1/slides/template-info/{template_id}
```

### 3.3 Tạo Slide Đồng bộ
```
POST /api/v1/slides/generate-slides
Content-Type: application/json

{
  "lesson_id": "lesson_123",
  "template_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "config_prompt": "Tạo slide với phong cách sinh động...",
  "presentation_title": "Bài học Toán 10"
}
```

### 3.4 Tạo Slide Bất đồng bộ
```
POST /api/v1/slides/generate-slides-async
Content-Type: application/json

{
  "lesson_id": "lesson_123",
  "template_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "config_prompt": "Tạo slide với phong cách sinh động..."
}
```

## 4. Quy trình hoạt động

### 4.1 Đồng bộ (Sync)
1. Client gửi request với `lesson_id` và `template_id`
2. Server lấy nội dung bài học từ Qdrant
3. Phân tích cấu trúc template Google Slides
4. LLM sinh nội dung slide phù hợp
5. Tạo Google Slides mới và trả về link

### 4.2 Bất đồng bộ (Async)
1. Client gửi request và nhận `task_id`
2. Celery task xử lý trong background
3. Client theo dõi progress qua `/api/v1/tasks/{task_id}/status`
4. Khi hoàn thành, lấy kết quả từ task result

## 5. Testing

### 5.1 Test cơ bản
```bash
python simple_test.py
```

### 5.2 Test đầy đủ
```bash
python test_slide_api.py
```

### 5.3 Test qua Swagger UI
Truy cập: http://localhost:8000/api/v1/docs

## 6. Troubleshooting

### Lỗi "Google Slides API has not been used"
- Enable Google Slides API trong Google Cloud Console
- Đợi vài phút để API được kích hoạt

### Lỗi "Service not available"
- Kiểm tra `GOOGLE_DRIVE_CREDENTIALS_PATH` trong `.env`
- Đảm bảo service account có quyền truy cập

### Lỗi "Lesson not found"
- Kiểm tra `lesson_id` có tồn tại trong Qdrant
- Sử dụng endpoint `/api/v1/pdf/textbooks` để xem danh sách lessons

### Lỗi "Template not accessible"
- Kiểm tra `template_id` đúng format
- Đảm bảo template có quyền truy cập public hoặc shared với service account

## 7. Cấu trúc Files đã tạo

```
app/
├── services/
│   ├── google_slides_service.py      # Google Slides API integration
│   └── slide_generation_service.py   # Logic sinh nội dung slide
├── api/endpoints/
│   └── slide_generation.py           # API endpoints
├── models/
│   └── slide_generation_models.py    # Pydantic models
└── tasks/
    └── slide_generation_tasks.py     # Celery tasks

test_slide_api.py                     # Test script đầy đủ
simple_test.py                        # Test script đơn giản
```

## 8. Next Steps

1. **Enable Google Slides API** trong Google Cloud Console
2. **Tạo Service Account** và cấu hình credentials
3. **Test với template thực tế** và lesson_id có trong database
4. **Tối ưu prompt** cho LLM để sinh nội dung slide tốt hơn
5. **Thêm image generation** nếu cần ảnh trong slides
6. **Implement cleanup task** để xóa presentations cũ
